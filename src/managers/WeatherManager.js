/**
 * 天气数据管理器
 * 负责获取云南省各地区的天气数据
 */

import { WEATHER_API_CONFIG, YUNNAN_PLACES, PLACE_NAME_MAPPING, API_CONFIG } from '../data/weatherConfig.js'
import { getAllCounties } from '../data/yunnanCountiesComplete.js'

export class WeatherManager {
  constructor() {
    this.weatherData = new Map() // 存储天气数据
    this.isLoading = false
    this.lastUpdateTime = null
    this.updateCallbacks = [] // 数据更新回调
  }

  /**
   * 添加数据更新回调
   * @param {Function} callback - 回调函数
   */
  onDataUpdate(callback) {
    this.updateCallbacks.push(callback)
  }

  /**
   * 移除数据更新回调
   * @param {Function} callback - 回调函数
   */
  offDataUpdate(callback) {
    const index = this.updateCallbacks.indexOf(callback)
    if (index > -1) {
      this.updateCallbacks.splice(index, 1)
    }
  }

  /**
   * 触发数据更新回调
   */
  triggerDataUpdate() {
    this.updateCallbacks.forEach(callback => {
      try {
        callback(this.weatherData)
      } catch (error) {
        console.error('天气数据更新回调执行失败:', error)
      }
    })
  }

  /**
   * 获取单个地区的天气数据
   * @param {string} place - 地区名称
   * @returns {Promise<Object>} 天气数据
   */
  async fetchWeatherData(place) {
    const url = new URL(WEATHER_API_CONFIG.baseUrl)
    url.searchParams.set('id', WEATHER_API_CONFIG.id)
    url.searchParams.set('key', WEATHER_API_CONFIG.key)
    url.searchParams.set('sheng', WEATHER_API_CONFIG.sheng)
    url.searchParams.set('place', place)

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        timeout: API_CONFIG.timeout
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.code !== 200) {
        throw new Error(`API错误: ${data.code}`)
      }

      return {
        place: data.place,
        temperature: data.temperature,
        humidity: data.humidity,
        pressure: data.pressure,
        windDirection: data.windDirection,
        windSpeed: data.windSpeed,
        weather: data.weather1,
        weatherImg: data.weather1img,
        updateTime: data.uptime,
        feelst: data.feelst,
        precipitation: data.precipitation || 0 // 降水量，默认为0
      }
    } catch (error) {
      console.error(`获取${place}天气数据失败:`, error)
      throw error
    }
  }

  /**
   * 批量获取天气数据
   * @param {Array} places - 地区名称数组
   * @returns {Promise<Map>} 天气数据Map
   */
  async fetchBatchWeatherData(places) {
    const results = new Map()
    const errors = []

    // 分批处理请求，避免并发过多
    for (let i = 0; i < places.length; i += API_CONFIG.batchSize) {
      const batch = places.slice(i, i + API_CONFIG.batchSize)
      
      const batchPromises = batch.map(async (place) => {
        try {
          // 添加请求间隔
          await new Promise(resolve => setTimeout(resolve, API_CONFIG.requestInterval))
          
          const data = await this.fetchWeatherData(place)
          return { place, data, success: true }
        } catch (error) {
          errors.push({ place, error })
          return { place, error, success: false }
        }
      })

      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value.success) {
          results.set(result.value.place, result.value.data)
        }
      })

      // 批次间延迟
      if (i + API_CONFIG.batchSize < places.length) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    if (errors.length > 0) {
      console.warn(`${errors.length}个地区天气数据获取失败:`, errors)
    }

    return results
  }

  /**
   * 生成模拟天气数据（用于演示）
   * @returns {Map} 模拟的天气数据
   */
  generateMockWeatherData() {
    const mockData = new Map()

    // 云南主要城市的模拟天气数据，包含更多样化的天气状况
    const cities = [
      { name: '昆明', temp: 18.5, weather: '小雨' },
      { name: '大理', temp: 16.2, weather: '晴' },
      { name: '丽江', temp: 14.8, weather: '中雨' },
      { name: '香格里拉', temp: 8.3, weather: '小雪' },
      { name: '西双版纳', temp: 25.6, weather: '阵雨' },
      { name: '曲靖', temp: 15.9, weather: '多云' },
      { name: '玉溪', temp: 17.4, weather: '雷阵雨' },
      { name: '保山', temp: 19.1, weather: '大雨' },
      { name: '昭通', temp: 12.7, weather: '阴' },
      { name: '普洱', temp: 21.3, weather: '阵雨' },
      { name: '临沧', temp: 20.8, weather: '晴' },
      { name: '楚雄', temp: 16.5, weather: '多云' },
      { name: '红河', temp: 22.1, weather: '小雨' },
      { name: '文山', temp: 18.9, weather: '中雨' },
      { name: '德宏', temp: 23.4, weather: '雷阵雨' },
      { name: '怒江', temp: 13.6, weather: '中雪' },
      { name: '迪庆', temp: 7.2, weather: '大雪' }
    ]

    cities.forEach(city => {
      // 根据天气状况生成合理的降水量数据
      let precipitation = 0
      if (city.weather.includes('雨')) {
        if (city.weather.includes('小雨')) {
          precipitation = Math.random() * 10 + 2 // 2-12mm
        } else if (city.weather.includes('中雨')) {
          precipitation = Math.random() * 20 + 10 // 10-30mm
        } else if (city.weather.includes('大雨')) {
          precipitation = Math.random() * 30 + 25 // 25-55mm
        } else {
          precipitation = Math.random() * 15 + 5 // 5-20mm
        }
      } else if (city.weather.includes('雪')) {
        precipitation = Math.random() * 8 + 1 // 1-9mm（雪的降水量相对较少）
      }

      mockData.set(city.name, {
        place: `中国, 云南, ${city.name}`,
        temperature: city.temp,
        humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
        pressure: Math.floor(Math.random() * 100) + 950, // 950-1050hPa
        windDirection: ['东风', '西风', '南风', '北风', '东南风', '西北风'][Math.floor(Math.random() * 6)],
        windSpeed: Math.random() * 10 + 1, // 1-11 m/s
        weather: city.weather,
        weather1: city.weather, // 添加weather1字段，与API格式保持一致
        weatherImg: `https://rescdn.apihz.cn/resimg/tianqi/${city.weather}.png`,
        updateTime: new Date().toLocaleString('zh-CN'),
        feelst: city.temp + (Math.random() - 0.5) * 4, // 体感温度
        precipitation: 0 // 模拟API返回的precipitation始终为0的情况
      })
    })

    return mockData
  }

  /**
   * 更新所有地区的天气数据
   * @returns {Promise<boolean>} 是否成功
   */
  async updateAllWeatherData() {
    if (this.isLoading) {
      console.warn('天气数据正在更新中，请稍后再试')
      return false
    }

    try {
      this.isLoading = true
      console.log('开始更新天气数据...')

      // 先尝试使用真实API，如果失败则使用模拟数据
      try {
        // 测试单个API调用
        const testData = await this.fetchWeatherData('昆明')
        console.log('API测试成功，使用真实数据')

        // 获取所有地区的天气数据
        const weatherData = await this.fetchBatchWeatherData(YUNNAN_PLACES)
        this.weatherData = weatherData
      } catch (apiError) {
        console.warn('天气API调用失败，使用模拟数据:', apiError.message)

        // 使用模拟数据
        this.weatherData = this.generateMockWeatherData()
      }

      this.lastUpdateTime = new Date()

      console.log(`天气数据更新完成，共获取${this.weatherData.size}个地区的数据`)

      // 触发更新回调
      this.triggerDataUpdate()

      return true
    } catch (error) {
      console.error('天气数据更新失败:', error)
      return false
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 获取热力图数据点
   * @returns {Array} 热力图数据点数组
   */
  getHeatmapData() {
    const heatmapData = []
    const counties = getAllCounties()

    console.log(`开始生成热力图数据，共${counties.length}个区县`)
    console.log('可用天气数据:', Array.from(this.weatherData.keys()))

    let matchedCount = 0
    let unmatchedCount = 0

    // 遍历所有区县，匹配天气数据
    counties.forEach(county => {
      const weatherInfo = this.findWeatherDataForCounty(county.name)
      if (weatherInfo && weatherInfo.temperature !== undefined) {
        heatmapData.push({
          lng: county.center[0],
          lat: county.center[1],
          count: this.normalizeTemperature(weatherInfo.temperature),
          temperature: weatherInfo.temperature,
          place: county.name,
          weather: weatherInfo.weather
        })
        matchedCount++
      } else {
        unmatchedCount++
        // 只记录前10个未匹配的区县，避免日志过多
        if (unmatchedCount <= 10) {
          console.log(`未找到天气数据的区县: ${county.name}`)
        }
      }
    })

    console.log(`热力图数据生成完成: 匹配${matchedCount}个，未匹配${unmatchedCount}个`)
    return heatmapData
  }

  /**
   * 获取降水量热力图数据点
   * @returns {Array} 降水量热力图数据点数组
   */
  getPrecipitationHeatmapData() {
    const heatmapData = []
    const counties = getAllCounties()

    console.log(`开始生成降水量热力图数据，共${counties.length}个区县`)
    console.log('可用天气数据:', Array.from(this.weatherData.keys()))

    let matchedCount = 0
    let unmatchedCount = 0

    // 遍历所有区县，匹配天气数据
    counties.forEach(county => {
      const weatherInfo = this.findWeatherDataForCounty(county.name)
      if (weatherInfo && weatherInfo.weather1) {
        // 使用weather1字段来判断降水量级别
        const precipitationLevel = this.getPrecipitationLevelFromWeather(weatherInfo.weather1)
        heatmapData.push({
          lng: county.center[0],
          lat: county.center[1],
          count: precipitationLevel,
          precipitation: precipitationLevel, // 使用计算出的降水量级别
          place: county.name,
          weather: weatherInfo.weather1,
          originalWeather: weatherInfo.weather1
        })
        matchedCount++
      } else {
        unmatchedCount++
        // 只记录前10个未匹配的区县，避免日志过多
        if (unmatchedCount <= 10) {
          console.log(`未找到天气数据的区县: ${county.name}`)
        }
      }
    })

    console.log(`降水量热力图数据生成完成: 匹配${matchedCount}个，未匹配${unmatchedCount}个`)
    return heatmapData
  }

  /**
   * 为区县查找对应的天气数据
   * @param {string} countyName - 区县名称
   * @returns {Object|null} 天气数据
   */
  findWeatherDataForCounty(countyName) {
    // 1. 直接匹配
    if (this.weatherData.has(countyName)) {
      return this.weatherData.get(countyName)
    }

    // 2. 去掉区县后缀匹配
    const simpleName = countyName.replace(/[区县市]/g, '')
    if (this.weatherData.has(simpleName)) {
      return this.weatherData.get(simpleName)
    }

    // 3. 根据区县名称推断所属城市
    const cityName = this.getCityNameFromCounty(countyName)
    if (cityName && this.weatherData.has(cityName)) {
      return this.weatherData.get(cityName)
    }

    // 4. 模糊匹配
    for (const [place, data] of this.weatherData) {
      if (place.includes(simpleName) || simpleName.includes(place)) {
        return data
      }
    }

    return null
  }

  /**
   * 根据区县名称推断所属城市
   * @param {string} countyName - 区县名称
   * @returns {string|null} 城市名称
   */
  getCityNameFromCounty(countyName) {
    // 昆明市区县
    if (['五华区', '盘龙区', '官渡区', '西山区', '东川区', '呈贡区', '晋宁区',
         '富民县', '宜良县', '石林彝族自治县', '嵩明县', '禄劝彝族苗族自治县',
         '寻甸回族彝族自治县', '安宁市'].includes(countyName)) {
      return '昆明'
    }

    // 曲靖市区县
    if (['麒麟区', '沾益区', '马龙区', '陆良县', '师宗县', '罗平县', '富源县', '会泽县', '宣威市'].includes(countyName)) {
      return '曲靖'
    }

    // 玉溪市区县
    if (['红塔区', '江川区', '澄江市', '通海县', '华宁县', '易门县', '峨山彝族自治县', '新平彝族傣族自治县', '元江哈尼族彝族傣族自治县'].includes(countyName)) {
      return '玉溪'
    }

    // 保山市区县
    if (['隆阳区', '施甸县', '龙陵县', '昌宁县', '腾冲市'].includes(countyName)) {
      return '保山'
    }

    // 昭通市区县
    if (['昭阳区', '鲁甸县', '巧家县', '盐津县', '大关县', '永善县', '绥江县', '镇雄县', '彝良县', '威信县', '水富市'].includes(countyName)) {
      return '昭通'
    }

    // 丽江市区县
    if (['古城区', '玉龙纳西族自治县', '永胜县', '华坪县', '宁蒗彝族自治县'].includes(countyName)) {
      return '丽江'
    }

    // 普洱市区县
    if (['思茅区', '宁洱哈尼族彝族自治县', '墨江哈尼族自治县', '景东彝族自治县', '景谷傣族彝族自治县',
         '镇沅彝族哈尼族拉祜族自治县', '江城哈尼族彝族自治县', '孟连傣族拉祜族佤族自治县',
         '澜沧拉祜族自治县', '西盟佤族自治县'].includes(countyName)) {
      return '普洱'
    }

    // 临沧市区县
    if (['临翔区', '凤庆县', '云县', '永德县', '镇康县', '双江拉祜族佤族布朗族傣族自治县',
         '耿马傣族佤族自治县', '沧源佤族自治县'].includes(countyName)) {
      return '临沧'
    }

    // 楚雄州区县
    if (['楚雄市', '双柏县', '牟定县', '南华县', '姚安县', '大姚县', '永仁县', '元谋县', '武定县', '禄丰市'].includes(countyName)) {
      return '楚雄'
    }

    // 红河州区县
    if (['个旧市', '开远市', '蒙自市', '屏边苗族自治县', '建水县', '石屏县', '弥勒市', '泸西县',
         '元阳县', '红河县', '金平苗族瑶族傣族自治县', '绿春县', '河口瑶族自治县'].includes(countyName)) {
      return '红河'
    }

    // 文山州区县
    if (['文山市', '砚山县', '西畴县', '麻栗坡县', '马关县', '丘北县', '广南县', '富宁县'].includes(countyName)) {
      return '文山'
    }

    // 西双版纳州区县
    if (['景洪市', '勐海县', '勐腊县'].includes(countyName)) {
      return '西双版纳'
    }

    // 大理州区县
    if (['大理市', '漾濞彝族自治县', '祥云县', '宾川县', '弥渡县', '南涧彝族自治县', '巍山彝族回族自治县',
         '永平县', '云龙县', '洱源县', '剑川县', '鹤庆县'].includes(countyName)) {
      return '大理'
    }

    // 德宏州区县
    if (['瑞丽市', '芒市', '梁河县', '盈江县', '陇川县'].includes(countyName)) {
      return '德宏'
    }

    // 怒江州区县
    if (['泸水市', '福贡县', '贡山独龙族怒族自治县', '兰坪白族普米族自治县'].includes(countyName)) {
      return '怒江'
    }

    // 迪庆州区县
    if (['香格里拉市', '德钦县', '维西傈僳族自治县'].includes(countyName)) {
      return '迪庆'
    }

    return null
  }

  /**
   * 将温度值标准化为热力图强度值
   * @param {number} temperature - 温度值
   * @returns {number} 标准化后的强度值
   */
  normalizeTemperature(temperature) {
    const minTemp = -10
    const maxTemp = 40
    const normalized = (temperature - minTemp) / (maxTemp - minTemp)
    return Math.max(0, Math.min(100, normalized * 100))
  }

  /**
   * 根据天气状况获取降水量级别
   * @param {string} weather - 天气状况（如：晴、小雨、中雨等）
   * @returns {number} 降水量级别（0-100）
   */
  getPrecipitationLevelFromWeather(weather) {
    if (!weather) return 0

    // 定义天气状况对应的降水量级别
    const weatherLevels = {
      // 无降水
      '晴': 0,
      '阴': 0,
      '多云': 0,

      // 雨类降水
      '小雨': 20,
      '阵雨': 25,
      '中雨': 40,
      '大雨': 60,
      '暴雨': 80,
      '雷阵雨': 35,

      // 雪类降水
      '小雪': 15,
      '阵雪': 20,
      '中雪': 35,
      '大雪': 55,
      '暴雪': 75,
      '雨夹雪': 30,
      '雷阵雪': 40
    }

    return weatherLevels[weather] || 0
  }

  /**
   * 将降水量值标准化为热力图强度值
   * @param {number} precipitation - 降水量值（毫米）
   * @returns {number} 标准化后的强度值
   */
  normalizePrecipitation(precipitation) {
    const minPrecipitation = 0
    const maxPrecipitation = 100
    const normalized = (precipitation - minPrecipitation) / (maxPrecipitation - minPrecipitation)
    return Math.max(0, Math.min(100, normalized * 100))
  }

  /**
   * 获取天气数据统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    if (this.weatherData.size === 0) {
      return null
    }

    const temperatures = Array.from(this.weatherData.values())
      .map(data => data.temperature)
      .filter(temp => temp !== undefined)

    if (temperatures.length === 0) {
      return null
    }

    const minTemp = Math.min(...temperatures)
    const maxTemp = Math.max(...temperatures)
    const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length

    return {
      totalPlaces: this.weatherData.size,
      minTemperature: minTemp,
      maxTemperature: maxTemp,
      averageTemperature: Math.round(avgTemp * 10) / 10,
      lastUpdateTime: this.lastUpdateTime
    }
  }

  /**
   * 获取降水量数据统计信息
   * @returns {Object} 降水量统计信息
   */
  getPrecipitationStatistics() {
    if (this.weatherData.size === 0) {
      return null
    }

    const precipitations = Array.from(this.weatherData.values())
      .map(data => data.precipitation)
      .filter(precip => precip !== undefined)

    if (precipitations.length === 0) {
      return null
    }

    const minPrecip = Math.min(...precipitations)
    const maxPrecip = Math.max(...precipitations)
    const avgPrecip = precipitations.reduce((sum, precip) => sum + precip, 0) / precipitations.length

    return {
      totalPlaces: this.weatherData.size,
      minPrecipitation: minPrecip,
      maxPrecipitation: maxPrecip,
      averagePrecipitation: Math.round(avgPrecip * 10) / 10,
      lastUpdateTime: this.lastUpdateTime
    }
  }

  /**
   * 清空天气数据
   */
  clearData() {
    this.weatherData.clear()
    this.lastUpdateTime = null
    this.triggerDataUpdate()
  }

  /**
   * 获取加载状态
   * @returns {boolean} 是否正在加载
   */
  isLoadingData() {
    return this.isLoading
  }
}

// 创建全局实例
export const weatherManager = new WeatherManager()
