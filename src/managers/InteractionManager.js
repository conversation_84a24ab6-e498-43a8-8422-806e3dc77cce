/**
 * 交互管理器 - 完全照搬1.4版本实现并适配2.0 API
 * 处理地图上所有元素的鼠标交互事件
 * 管理悬停提示框和详细信息面板的显示
 */

export class InteractionManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    
    // 交互状态 - 照搬1.4版本
    this.currentHoverTarget = null
    this.currentDetailTarget = null
    
    // 悬停提示框状态 - 照搬1.4版本
    this.hoverTooltipVisible = false
    this.hoverTooltipData = null
    this.hoverTooltipPosition = null
    
    // 详细信息面板状态 - 照搬1.4版本
    this.detailPanelVisible = false
    this.detailPanelData = null
    this.detailPanelType = null
    
    // 事件监听器 - 照搬1.4版本
    this.eventListeners = new Map()
    
    // 线路对象引用（由PowerLineRenderer提供） - 照搬1.4版本
    this.lineObjects = new Map()
    
    console.log('InteractionManager 初始化完成 (照搬1.4版本实现)')
  }

  /**
   * 初始化交互事件 - 照搬1.4版本实现
   * @param {Object} powerLineRenderer - 线路渲染器实例
   * @param {Object} massMarkerManager - 海量点管理器实例
   */
  initInteractions(powerLineRenderer, massMarkerManager) {
    // 设置线路交互事件
    this.setupLineInteractions(powerLineRenderer)
    
    // 设置海量点交互事件
    this.setupMassMarkerInteractions(massMarkerManager)
    
    // 设置地图点击事件
    this.setupMapClickEvents()
    
    console.log('交互事件初始化完成')
  }

  /**
   * 设置线路交互事件 - 照搬1.4版本实现
   * @param {Object} powerLineRenderer - 线路渲染器实例
   */
  setupLineInteractions(powerLineRenderer) {
    // 监听线路渲染完成事件
    powerLineRenderer.on('renderComplete', (summary) => {
      this.updateLineObjects(powerLineRenderer.lineObjects)
    })
    
    // 为每个线路对象添加事件监听 - 照搬1.4版本方法
    this.setupLineHoverEvents()
    this.setupLineClickEvents()
  }

  /**
   * 更新线路对象引用 - 照搬1.4版本实现
   * @param {Map} lineObjects - 线路对象Map
   */
  updateLineObjects(lineObjects) {
    this.lineObjects = lineObjects

    // 为新的线路对象添加事件监听 - 适配2.0版本的on/off方法
    this.lineObjects.forEach((lineObjectArray, lineId) => {
      lineObjectArray.forEach(lineObj => {
        this.addLineEventListeners(lineObj)
      })
    })

    let totalPolylines = 0
    this.lineObjects.forEach((lineObjectArray) => {
      totalPolylines += lineObjectArray.length
    })
    console.log(`已更新 ${this.lineObjects.size} 条线路的对象引用，共 ${totalPolylines} 个Polyline对象`)
  }

  /**
   * 为线路对象添加事件监听 - 适配2.0版本的on/off方法
   * @param {Object} lineObj - 线路对象
   */
  addLineEventListeners(lineObj) {
    // 使用2.0版本推荐的on方法绑定事件，替代1.4版本的AMap.event.addEventListener
    console.log('为线路对象绑定事件监听器')

    lineObj.on('mouseover', (e) => {
      console.log('线路mouseover事件触发')
      this.handleLineMouseOver(e)
    })

    lineObj.on('mouseout', (e) => {
      console.log('线路mouseout事件触发')
      this.handleLineMouseOut(e)
    })

    lineObj.on('click', (e) => {
      console.log('线路click事件触发')
      this.handleLineClick(e)
    })

    console.log('线路事件监听器绑定完成')
  }

  /**
   * 设置线路悬停事件 - 照搬1.4版本实现
   */
  setupLineHoverEvents() {
    // 这个方法在1.4版本中主要是为了兼容性，实际事件绑定在addLineEventListeners中
    console.log('线路悬停事件设置完成')
  }

  /**
   * 设置线路点击事件 - 照搬1.4版本实现
   */
  setupLineClickEvents() {
    // 这个方法在1.4版本中主要是为了兼容性，实际事件绑定在addLineEventListeners中
    console.log('线路点击事件设置完成')
  }

  /**
   * 处理线路鼠标悬停事件 - 照搬1.4版本实现
   * @param {Object} e - 事件对象
   */
  handleLineMouseOver(e) {
    console.log('线路鼠标悬停事件')

    const extData = e.target.getExtData()
    console.log('线路扩展数据:', extData)

    if (extData && extData.type === 'powerline') {
      // 显示悬停提示框
      this.showHoverTooltip({
        type: 'powerline',
        lineId: extData.lineId,
        lineName: extData.lineName,
        voltageLevel: extData.voltageLevel,
        status: extData.status,
        length: extData.length,
        lineLevel: extData.lineLevel,
        temperature: extData.temperature, // 添加气温字段
        weather: extData.weather // 添加天气字段
      }, {
        x: e.pixel.x,
        y: e.pixel.y
      })

      this.currentHoverTarget = e.target
    }
  }

  /**
   * 处理线路鼠标离开事件 - 照搬1.4版本实现
   * @param {Object} e - 事件对象
   */
  handleLineMouseOut(e) {
    console.log('线路鼠标离开事件')

    // 隐藏悬停提示框
    this.hideHoverTooltip()
    this.currentHoverTarget = null
  }

  /**
   * 处理线路点击事件 - 照搬1.4版本实现
   * @param {Object} e - 事件对象
   */
  handleLineClick(e) {
    console.log('线路点击事件')

    const extData = e.target.getExtData()
    console.log('线路扩展数据:', extData)

    if (extData && extData.type === 'powerline') {
      console.log('显示线路详细面板:', extData.lineName)
      this.showDetailPanel({
        type: 'powerline',
        lineId: extData.lineId,
        lineName: extData.lineName,
        voltageLevel: extData.voltageLevel,
        status: extData.status,
        length: extData.length,
        lineLevel: extData.lineLevel,
        constructionProgress: extData.constructionProgress,
        segments: extData.segments,
        description: extData.description,
        detailInfo: extData.detailInfo
      }, 'powerline')

      this.currentDetailTarget = e.target
    }
  }

  /**
   * 设置海量点交互事件 - 照搬1.4版本实现
   * @param {Object} massMarkerManager - 海量点管理器实例
   */
  setupMassMarkerInteractions(massMarkerManager) {
    // 监听电塔点击事件
    massMarkerManager.on('towerClick', (data) => {
      this.handleTowerClick(data)
    })
    
    // 监听变电站点击事件
    massMarkerManager.on('substationClick', (data) => {
      this.handleSubstationClick(data)
    })
    
    // 监听发电站点击事件
    massMarkerManager.on('powerPlantClick', (data) => {
      this.handlePowerPlantClick(data)
    })
    
    console.log('海量点交互事件设置完成')
  }

  /**
   * 设置地图点击事件 - 照搬1.4版本实现
   */
  setupMapClickEvents() {
    // 适配2.0版本的事件绑定方式
    this.map.on('click', (e) => {
      // 检查是否点击在空白区域
      if (!e.target || e.target === this.map) {
        this.hideDetailPanel()
      }
    })
    
    console.log('地图点击事件设置完成')
  }

  /**
   * 处理电塔点击事件 - 照搬1.4版本实现
   * @param {Object} data - 事件数据
   */
  handleTowerClick(data) {
    console.log('InteractionManager 处理电塔点击:', data)
    this.showDetailPanel({
      type: 'tower',
      towerId: data.data.towerId,
      towerName: data.data.towerId, // 使用towerId作为名称
      lineId: data.data.lineId,
      lineName: data.data.powerlineName, // 使用powerlineName字段
      voltageLevel: data.data.voltageLevel,
      detailInfo: data.data.detailInfo
    }, 'tower')
  }

  /**
   * 处理变电站点击事件 - 照搬1.4版本实现
   * @param {Object} data - 事件数据
   */
  handleSubstationClick(data) {
    console.log('InteractionManager 处理变电站点击:', data)
    this.showDetailPanel({
      type: 'substation',
      stationId: data.data.stationId,
      stationName: data.data.stationName,
      voltageLevel: data.data.voltageLevel,
      capacity: data.data.capacity,
      detailInfo: data.data.detailInfo
    }, 'substation')
  }

  /**
   * 处理发电站点击事件 - 照搬1.4版本实现
   * @param {Object} data - 事件数据
   */
  handlePowerPlantClick(data) {
    console.log('InteractionManager 处理发电站点击:', data)
    this.showDetailPanel({
      type: 'powerPlant',
      plantId: data.data.plantId,
      plantName: data.data.plantName,
      capacity: data.data.capacity,
      generationType: data.data.generationType,
      detailInfo: data.data.detailInfo
    }, 'powerPlant')
  }

  /**
   * 显示悬停提示框 - 照搬1.4版本实现
   * @param {Object} data - 提示数据
   * @param {Object} position - 位置信息
   */
  showHoverTooltip(data, position) {
    this.hoverTooltipData = data
    this.hoverTooltipPosition = position
    this.hoverTooltipVisible = true

    this.emit('showHoverTooltip', {
      data: data,
      position: position,
      visible: true
    })
  }

  /**
   * 隐藏悬停提示框 - 照搬1.4版本实现
   */
  hideHoverTooltip() {
    this.hoverTooltipVisible = false
    this.hoverTooltipData = null
    this.hoverTooltipPosition = null

    this.emit('hideHoverTooltip', {
      visible: false
    })
  }

  /**
   * 显示详细信息面板 - 照搬1.4版本实现
   * @param {Object} data - 详细信息数据
   * @param {string} type - 面板类型
   */
  showDetailPanel(data, type) {
    this.detailPanelData = data
    this.detailPanelType = type
    this.detailPanelVisible = true

    this.emit('showDetailPanel', {
      data: data,
      type: type,
      visible: true
    })
  }

  /**
   * 隐藏详细信息面板 - 照搬1.4版本实现
   */
  hideDetailPanel() {
    this.detailPanelVisible = false
    this.detailPanelData = null
    this.detailPanelType = null
    this.currentDetailTarget = null

    this.emit('hideDetailPanel', {
      visible: false
    })
  }

  /**
   * 获取当前状态 - 照搬1.4版本实现
   * @returns {Object} 当前状态
   */
  getCurrentState() {
    return {
      hoverTooltip: {
        visible: this.hoverTooltipVisible,
        data: this.hoverTooltipData,
        position: this.hoverTooltipPosition
      },
      detailPanel: {
        visible: this.detailPanelVisible,
        data: this.detailPanelData,
        type: this.detailPanelType
      }
    }
  }

  /**
   * 事件发射器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器执行失败 ${eventName}:`, error)
        }
      })
    }
  }

  /**
   * 添加事件监听器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    this.eventListeners.get(eventName).push(listener)
  }

  /**
   * 移除事件监听器 - 照搬1.4版本实现
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(eventName, listener) {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 销毁交互管理器 - 照搬1.4版本实现
   */
  destroy() {
    // 清除所有事件监听器
    this.eventListeners.clear()

    // 重置状态
    this.currentHoverTarget = null
    this.currentDetailTarget = null
    this.hoverTooltipVisible = false
    this.hoverTooltipData = null
    this.hoverTooltipPosition = null
    this.detailPanelVisible = false
    this.detailPanelData = null
    this.detailPanelType = null

    console.log('InteractionManager 已销毁')
  }
}
